import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8001/api'

class ApiClient {
  private client: AxiosInstance

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'application/json',
      },
    })

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        const token = this.getToken()
        if (token) {
          config.headers.Authorization = `Bearer ${token}`
        }
        return config
      },
      (error) => {
        return Promise.reject(error)
      }
    )

    // Response interceptor for error handling
    this.client.interceptors.response.use(
      (response) => response,
      (error) => {
        if (error.response?.status === 401) {
          this.removeToken()
          // Redirect to login if needed
          if (typeof window !== 'undefined') {
            window.location.href = '/auth/login'
          }
        }
        return Promise.reject(error)
      }
    )
  }

  private getToken(): string | null {
    if (typeof window === 'undefined') return null
    return localStorage.getItem('auth_token')
  }

  private setToken(token: string): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', token)
    }
  }

  private removeToken(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token')
    }
  }

  // Auth methods
  async login(email: string, password: string) {
    const response = await this.client.post('/login', { email, password })
    if (response.data.token) {
      this.setToken(response.data.token)
    }
    return response.data
  }

  async register(userData: {
    name: string
    email: string
    password: string
    password_confirmation: string
    role?: string
  }) {
    const response = await this.client.post('/register', userData)
    if (response.data.token) {
      this.setToken(response.data.token)
    }
    return response.data
  }

  async logout() {
    try {
      await this.client.post('/logout')
    } finally {
      this.removeToken()
    }
  }

  // Properties methods
  async getProperties(params?: {
    search?: string
    type?: string
    min_price?: number
    max_price?: number
    bedrooms?: number
    location?: string
    page?: number
  }) {
    const response = await this.client.get('/properties', { params })
    return response.data
  }

  async getProperty(id: string) {
    const response = await this.client.get(`/properties/${id}`)
    return response.data
  }

  async likeProperty(id: string) {
    const response = await this.client.post(`/user/properties/${id}/like`)
    return response.data
  }

  // Booking methods
  async createBooking(propertyId: string, bookingData: {
    booking_datetime: string
    contact_info: string
  }) {
    const response = await this.client.post(`/user/properties/${propertyId}/book`, bookingData)
    return response.data
  }

  // Developer methods
  async getDeveloperProjects() {
    const response = await this.client.get('/developer/projects')
    return response.data
  }

  async createProject(projectData: {
    name: string
    description: string
    location: string
  }) {
    const response = await this.client.post('/developer/projects', projectData)
    return response.data
  }

  async updateProject(id: string, projectData: Partial<{
    name: string
    description: string
    location: string
  }>) {
    const response = await this.client.put(`/developer/projects/${id}`, projectData)
    return response.data
  }

  async deleteProject(id: string) {
    const response = await this.client.delete(`/developer/projects/${id}`)
    return response.data
  }

  async uploadProjectImage(projectId: string, file: File) {
    const formData = new FormData()
    formData.append('image', file)
    
    const response = await this.client.post(
      `/developer/projects/${projectId}/upload-image`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    )
    return response.data
  }

  // Admin methods
  async getAdminProjects() {
    const response = await this.client.get('/admin/projects')
    return response.data
  }

  async approveProject(id: string) {
    const response = await this.client.post(`/admin/projects/${id}/approve`)
    return response.data
  }

  async rejectProject(id: string) {
    const response = await this.client.post(`/admin/projects/${id}/reject`)
    return response.data
  }

  // Generic request method
  async request<T = any>(config: AxiosRequestConfig): Promise<AxiosResponse<T>> {
    return this.client.request(config)
  }
}

export const apiClient = new ApiClient()
export default apiClient

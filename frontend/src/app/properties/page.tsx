'use client'

import React, { useState, useEffect } from 'react'
import { motion } from 'framer-motion'
import { Search, Filter, MapPin, Bed, Bath, Square, Heart, Eye, Building2 } from 'lucide-react'
import { Button } from '@/components/ui/Button'
import { Input } from '@/components/ui/Input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { api } from '@/lib/api'
import { Property } from '@/types'

export default function PropertiesPage() {
  const [properties, setProperties] = useState<Property[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [filters, setFilters] = useState({
    type: '',
    minPrice: '',
    maxPrice: '',
    bedrooms: '',
    location: ''
  })

  useEffect(() => {
    fetchProperties()
  }, [])

  const fetchProperties = async () => {
    try {
      setLoading(true)
      const response = await api.get('/properties')
      setProperties(response.data.data || [])
    } catch (error) {
      console.error('Error fetching properties:', error)
      // Use mock data for demo
      setProperties([
        {
          id: 1,
          project_id: 1,
          title: 'Modern Luxury Villa',
          description: 'Beautiful modern villa with stunning views',
          price: 850000,
          bedrooms: 4,
          bathrooms: 3,
          size_sqm: 250.0,
          type: 'villa',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: 2,
          title: 'Cozy Family Apartment',
          description: 'Perfect for families with modern amenities',
          price: 450000,
          location: 'Green Valley',
          bedrooms: 3,
          bathrooms: 2,
          size: 1800,
          type: 'apartment',
          status: 'available',
          images: [],
          developer_id: 2,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        },
        {
          id: 3,
          title: 'Executive Penthouse',
          description: 'Luxury penthouse with panoramic city views',
          price: 1200000,
          location: 'City Center',
          bedrooms: 5,
          bathrooms: 4,
          size: 3200,
          type: 'penthouse',
          status: 'available',
          images: [],
          developer_id: 3,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
        }
      ])
    } finally {
      setLoading(false)
    }
  }

  const filteredProperties = properties.filter(property => {
    const matchesSearch = property.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         property.location.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesType = !filters.type || property.type === filters.type
    const matchesMinPrice = !filters.minPrice || property.price >= parseInt(filters.minPrice)
    const matchesMaxPrice = !filters.maxPrice || property.price <= parseInt(filters.maxPrice)
    const matchesBedrooms = !filters.bedrooms || property.bedrooms >= parseInt(filters.bedrooms)
    const matchesLocation = !filters.location || property.location.toLowerCase().includes(filters.location.toLowerCase())

    return matchesSearch && matchesType && matchesMinPrice && matchesMaxPrice && matchesBedrooms && matchesLocation
  })

  return (
    <div className="min-h-screen bg-gradient-to-br from-green-50 via-white to-green-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-primary to-green-600 py-20">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="text-center text-white"
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              Find Your Perfect Property
            </h1>
            <p className="text-xl md:text-2xl text-green-100 mb-8 max-w-3xl mx-auto">
              Browse through our extensive collection of premium properties
            </p>
          </motion.div>
        </div>
      </section>

      {/* Search and Filters */}
      <section className="py-8 bg-white shadow-sm">
        <div className="container mx-auto px-4">
          <div className="flex flex-col lg:flex-row gap-4 items-center">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5" />
              <input
                type="text"
                placeholder="Search properties..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary focus:border-transparent"
              />
            </div>
            <div className="flex gap-4 flex-wrap">
              <select
                value={filters.type}
                onChange={(e) => setFilters({...filters, type: e.target.value})}
                className="px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary"
              >
                <option value="">All Types</option>
                <option value="apartment">Apartment</option>
                <option value="villa">Villa</option>
                <option value="penthouse">Penthouse</option>
                <option value="townhouse">Townhouse</option>
              </select>
              <select
                value={filters.bedrooms}
                onChange={(e) => setFilters({...filters, bedrooms: e.target.value})}
                className="px-4 py-3 border border-gray-200 rounded-lg focus:ring-2 focus:ring-primary"
              >
                <option value="">Any Bedrooms</option>
                <option value="1">1+ Bedrooms</option>
                <option value="2">2+ Bedrooms</option>
                <option value="3">3+ Bedrooms</option>
                <option value="4">4+ Bedrooms</option>
              </select>
              <Button variant="outline" className="border-primary text-primary hover:bg-primary hover:text-white">
                <Filter className="h-4 w-4 mr-2" />
                More Filters
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Properties Grid */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          {loading ? (
            <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
              {[...Array(6)].map((_, i) => (
                <div key={i} className="animate-pulse">
                  <div className="bg-gray-200 aspect-video rounded-lg mb-4"></div>
                  <div className="h-4 bg-gray-200 rounded mb-2"></div>
                  <div className="h-4 bg-gray-200 rounded w-3/4"></div>
                </div>
              ))}
            </div>
          ) : (
            <>
              <div className="flex justify-between items-center mb-8">
                <h2 className="text-2xl font-bold text-gray-800">
                  {filteredProperties.length} Properties Found
                </h2>
                <select className="px-4 py-2 border border-gray-200 rounded-lg">
                  <option>Sort by: Latest</option>
                  <option>Price: Low to High</option>
                  <option>Price: High to Low</option>
                  <option>Size: Largest First</option>
                </select>
              </div>

              <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
                {filteredProperties.map((property, index) => (
                  <motion.div
                    key={property.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6, delay: index * 0.1 }}
                    className="group"
                  >
                    <Card className="overflow-hidden hover:shadow-2xl transition-all duration-500 border-0 shadow-lg group-hover:scale-105 bg-white">
                      <div className="relative">
                        <div className="aspect-video bg-gradient-to-br from-green-100 to-green-200 flex items-center justify-center">
                          <Building2 className="h-16 w-16 text-primary" />
                        </div>
                        <div className="absolute top-4 right-4">
                          <div className="bg-white/90 backdrop-blur-sm rounded-full p-2 shadow-lg hover:bg-primary hover:text-white transition-colors cursor-pointer">
                            <Heart className="h-5 w-5" />
                          </div>
                        </div>
                        <div className="absolute bottom-4 left-4 bg-white/95 backdrop-blur-sm rounded-xl px-4 py-2 shadow-lg">
                          <span className="text-lg font-bold text-primary">
                            ${property.price.toLocaleString()}
                          </span>
                        </div>
                      </div>
                      <CardHeader className="pb-4">
                        <CardTitle className="text-xl font-bold text-gray-800 line-clamp-1">
                          {property.title}
                        </CardTitle>
                        <div className="flex items-center text-gray-600">
                          <MapPin className="h-5 w-5 mr-2 text-primary" />
                          <span className="text-base">{property.location}</span>
                        </div>
                      </CardHeader>
                      <CardContent>
                        <div className="flex items-center justify-between text-gray-600 mb-4">
                          <div className="flex items-center space-x-6">
                            <div className="flex items-center">
                              <Bed className="h-5 w-5 mr-2 text-primary" />
                              <span className="font-medium">{property.bedrooms}</span>
                            </div>
                            <div className="flex items-center">
                              <Bath className="h-5 w-5 mr-2 text-primary" />
                              <span className="font-medium">{property.bathrooms}</span>
                            </div>
                            <div className="flex items-center">
                              <Square className="h-5 w-5 mr-2 text-primary" />
                              <span className="font-medium">{property.size_sqm} sqm</span>
                            </div>
                          </div>
                        </div>
                        <Button className="w-full bg-gradient-to-r from-primary to-green-600 hover:from-green-600 hover:to-primary">
                          View Details
                        </Button>
                      </CardContent>
                    </Card>
                  </motion.div>
                ))}
              </div>
            </>
          )}
        </div>
      </section>
    </div>
  )
}

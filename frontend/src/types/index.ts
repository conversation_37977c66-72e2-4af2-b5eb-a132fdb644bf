export interface User {
  id: number
  name: string
  email: string
  role: 'admin' | 'developer' | 'user'
  email_verified_at?: string
  created_at: string
  updated_at: string
}

export interface DeveloperProfile {
  id: number
  user_id: number
  company_name: string
  logo_path?: string
  description?: string
  verified_at?: string
  created_at: string
  updated_at: string
  user?: User
}

export interface Project {
  id: number
  developer_profile_id: number
  name: string
  description: string
  location: string
  status: 'pending' | 'approved' | 'rejected'
  created_at: string
  updated_at: string
  developer_profile?: DeveloperProfile
  properties?: Property[]
  images?: ProjectImage[]
}

export interface Property {
  id: number
  project_id: number
  type: string
  title: string
  description: string
  size_sqm: number
  bedrooms: number
  bathrooms: number
  price: number
  created_at: string
  updated_at: string
  project?: Project
  images?: PropertyImage[]
  is_liked?: boolean
}

export interface ProjectImage {
  id: number
  project_id: number
  image_path: string
  alt_text?: string
  is_primary: boolean
  created_at: string
  updated_at: string
}

export interface PropertyImage {
  id: number
  property_id: number
  image_path: string
  alt_text?: string
  is_primary: boolean
  created_at: string
  updated_at: string
}

export interface Booking {
  id: number
  user_id: number
  property_id: number
  developer_profile_id: number
  booking_datetime: string
  contact_info: string
  status: 'pending' | 'confirmed' | 'cancelled' | 'completed'
  created_at: string
  updated_at: string
  user?: User
  property?: Property
  developer_profile?: DeveloperProfile
}

export interface Availability {
  id: number
  developer_profile_id: number
  day_of_week: string
  start_time: string
  end_time: string
  created_at: string
  updated_at: string
}

export interface AuthResponse {
  user: User
  token: string
  message?: string
}

export interface ApiResponse<T = any> {
  data: T
  message?: string
  status: string
}

export interface PaginatedResponse<T = any> {
  data: T[]
  current_page: number
  last_page: number
  per_page: number
  total: number
  from: number
  to: number
}

export interface PropertyFilters {
  search?: string
  type?: string
  min_price?: number
  max_price?: number
  bedrooms?: number
  bathrooms?: number
  location?: string
  size_min?: number
  size_max?: number
  sort_by?: 'price_asc' | 'price_desc' | 'size_asc' | 'size_desc' | 'newest' | 'oldest'
  page?: number
  per_page?: number
}

export interface BookingFormData {
  property_id: number
  booking_date: string
  booking_time: string
  contact_info: string
  message?: string
}

export interface ProjectFormData {
  name: string
  description: string
  location: string
}

export interface PropertyFormData {
  project_id: number
  type: string
  title: string
  description: string
  size_sqm: number
  bedrooms: number
  bathrooms: number
  price: number
}

export interface DeveloperProfileFormData {
  company_name: string
  description?: string
  logo?: File
}

export interface ContactFormData {
  name: string
  email: string
  phone?: string
  message: string
  property_id?: number
}

export type UserRole = 'admin' | 'developer' | 'user'
export type ProjectStatus = 'pending' | 'approved' | 'rejected'
export type BookingStatus = 'pending' | 'confirmed' | 'cancelled' | 'completed'
export type PropertyType = 'apartment' | 'villa' | 'townhouse' | 'penthouse' | 'studio'

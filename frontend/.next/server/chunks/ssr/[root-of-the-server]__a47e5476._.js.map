{"version": 3, "sources": [], "sections": [{"offset": {"line": 119, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/darna/frontend/src/lib/api.ts"], "sourcesContent": ["import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'\n\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:8001/api'\n\nclass ApiClient {\n  private client: AxiosInstance\n\n  constructor() {\n    this.client = axios.create({\n      baseURL: API_BASE_URL,\n      headers: {\n        'Content-Type': 'application/json',\n        'Accept': 'application/json',\n      },\n    })\n\n    // Request interceptor to add auth token\n    this.client.interceptors.request.use(\n      (config) => {\n        const token = this.getToken()\n        if (token) {\n          config.headers.Authorization = `Bearer ${token}`\n        }\n        return config\n      },\n      (error) => {\n        return Promise.reject(error)\n      }\n    )\n\n    // Response interceptor for error handling\n    this.client.interceptors.response.use(\n      (response) => response,\n      (error) => {\n        if (error.response?.status === 401) {\n          this.removeToken()\n          // Redirect to login if needed\n          if (typeof window !== 'undefined') {\n            window.location.href = '/auth/login'\n          }\n        }\n        return Promise.reject(error)\n      }\n    )\n  }\n\n  private getToken(): string | null {\n    if (typeof window === 'undefined') return null\n    return localStorage.getItem('auth_token')\n  }\n\n  private setToken(token: string): void {\n    if (typeof window !== 'undefined') {\n      localStorage.setItem('auth_token', token)\n    }\n  }\n\n  private removeToken(): void {\n    if (typeof window !== 'undefined') {\n      localStorage.removeItem('auth_token')\n    }\n  }\n\n  // Auth methods\n  async login(email: string, password: string) {\n    const response = await this.client.post('/login', { email, password })\n    if (response.data.token) {\n      this.setToken(response.data.token)\n    }\n    return response.data\n  }\n\n  async register(userData: {\n    name: string\n    email: string\n    password: string\n    password_confirmation: string\n    role?: string\n  }) {\n    const response = await this.client.post('/register', userData)\n    if (response.data.token) {\n      this.setToken(response.data.token)\n    }\n    return response.data\n  }\n\n  async logout() {\n    try {\n      await this.client.post('/logout')\n    } finally {\n      this.removeToken()\n    }\n  }\n\n  // Properties methods\n  async getProperties(params?: {\n    search?: string\n    type?: string\n    min_price?: number\n    max_price?: number\n    bedrooms?: number\n    location?: string\n    page?: number\n  }) {\n    const response = await this.client.get('/properties', { params })\n    return response.data\n  }\n\n  async getProperty(id: string) {\n    const response = await this.client.get(`/properties/${id}`)\n    return response.data\n  }\n\n  async likeProperty(id: string) {\n    const response = await this.client.post(`/user/properties/${id}/like`)\n    return response.data\n  }\n\n  // Booking methods\n  async createBooking(propertyId: string, bookingData: {\n    booking_datetime: string\n    contact_info: string\n  }) {\n    const response = await this.client.post(`/user/properties/${propertyId}/book`, bookingData)\n    return response.data\n  }\n\n  // Developer methods\n  async getDeveloperProjects() {\n    const response = await this.client.get('/developer/projects')\n    return response.data\n  }\n\n  async createProject(projectData: {\n    name: string\n    description: string\n    location: string\n  }) {\n    const response = await this.client.post('/developer/projects', projectData)\n    return response.data\n  }\n\n  async updateProject(id: string, projectData: Partial<{\n    name: string\n    description: string\n    location: string\n  }>) {\n    const response = await this.client.put(`/developer/projects/${id}`, projectData)\n    return response.data\n  }\n\n  async deleteProject(id: string) {\n    const response = await this.client.delete(`/developer/projects/${id}`)\n    return response.data\n  }\n\n  async uploadProjectImage(projectId: string, file: File) {\n    const formData = new FormData()\n    formData.append('image', file)\n    \n    const response = await this.client.post(\n      `/developer/projects/${projectId}/upload-image`,\n      formData,\n      {\n        headers: {\n          'Content-Type': 'multipart/form-data',\n        },\n      }\n    )\n    return response.data\n  }\n\n  // Admin methods\n  async getAdminProjects() {\n    const response = await this.client.get('/admin/projects')\n    return response.data\n  }\n\n  async approveProject(id: string) {\n    const response = await this.client.post(`/admin/projects/${id}/approve`)\n    return response.data\n  }\n\n  async rejectProject(id: string) {\n    const response = await this.client.post(`/admin/projects/${id}/reject`)\n    return response.data\n  }\n\n  // Generic request method\n  async request<T = any>(config: AxiosRequestConfig): Promise<AxiosResponse<T>> {\n    return this.client.request(config)\n  }\n}\n\nexport const apiClient = new ApiClient()\nexport default apiClient\n"], "names": [], "mappings": ";;;;AAAA;;AAEA,MAAM,eAAe,QAAQ,GAAG,CAAC,mBAAmB,IAAI;AAExD,MAAM;IACI,OAAqB;IAE7B,aAAc;QACZ,IAAI,CAAC,MAAM,GAAG,qIAAA,CAAA,UAAK,CAAC,MAAM,CAAC;YACzB,SAAS;YACT,SAAS;gBACP,gBAAgB;gBAChB,UAAU;YACZ;QACF;QAEA,wCAAwC;QACxC,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,OAAO,CAAC,GAAG,CAClC,CAAC;YACC,MAAM,QAAQ,IAAI,CAAC,QAAQ;YAC3B,IAAI,OAAO;gBACT,OAAO,OAAO,CAAC,aAAa,GAAG,CAAC,OAAO,EAAE,OAAO;YAClD;YACA,OAAO;QACT,GACA,CAAC;YACC,OAAO,QAAQ,MAAM,CAAC;QACxB;QAGF,0CAA0C;QAC1C,IAAI,CAAC,MAAM,CAAC,YAAY,CAAC,QAAQ,CAAC,GAAG,CACnC,CAAC,WAAa,UACd,CAAC;YACC,IAAI,MAAM,QAAQ,EAAE,WAAW,KAAK;gBAClC,IAAI,CAAC,WAAW;gBAChB,8BAA8B;gBAC9B,uCAAmC;;gBAEnC;YACF;YACA,OAAO,QAAQ,MAAM,CAAC;QACxB;IAEJ;IAEQ,WAA0B;QAChC,wCAAmC,OAAO;;IAE5C;IAEQ,SAAS,KAAa,EAAQ;QACpC,uCAAmC;;QAEnC;IACF;IAEQ,cAAoB;QAC1B,uCAAmC;;QAEnC;IACF;IAEA,eAAe;IACf,MAAM,MAAM,KAAa,EAAE,QAAgB,EAAE;QAC3C,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,UAAU;YAAE;YAAO;QAAS;QACpE,IAAI,SAAS,IAAI,CAAC,KAAK,EAAE;YACvB,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,KAAK;QACnC;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,SAAS,QAMd,EAAE;QACD,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,aAAa;QACrD,IAAI,SAAS,IAAI,CAAC,KAAK,EAAE;YACvB,IAAI,CAAC,QAAQ,CAAC,SAAS,IAAI,CAAC,KAAK;QACnC;QACA,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC;QACzB,SAAU;YACR,IAAI,CAAC,WAAW;QAClB;IACF;IAEA,qBAAqB;IACrB,MAAM,cAAc,MAQnB,EAAE;QACD,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,eAAe;YAAE;QAAO;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,YAAY,EAAU,EAAE;QAC5B,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,YAAY,EAAE,IAAI;QAC1D,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,aAAa,EAAU,EAAE;QAC7B,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,iBAAiB,EAAE,GAAG,KAAK,CAAC;QACrE,OAAO,SAAS,IAAI;IACtB;IAEA,kBAAkB;IAClB,MAAM,cAAc,UAAkB,EAAE,WAGvC,EAAE;QACD,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,iBAAiB,EAAE,WAAW,KAAK,CAAC,EAAE;QAC/E,OAAO,SAAS,IAAI;IACtB;IAEA,oBAAoB;IACpB,MAAM,uBAAuB;QAC3B,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;QACvC,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,cAAc,WAInB,EAAE;QACD,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,uBAAuB;QAC/D,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,cAAc,EAAU,EAAE,WAI9B,EAAE;QACF,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,oBAAoB,EAAE,IAAI,EAAE;QACpE,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,cAAc,EAAU,EAAE;QAC9B,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,oBAAoB,EAAE,IAAI;QACrE,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,mBAAmB,SAAiB,EAAE,IAAU,EAAE;QACtD,MAAM,WAAW,IAAI;QACrB,SAAS,MAAM,CAAC,SAAS;QAEzB,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CACrC,CAAC,oBAAoB,EAAE,UAAU,aAAa,CAAC,EAC/C,UACA;YACE,SAAS;gBACP,gBAAgB;YAClB;QACF;QAEF,OAAO,SAAS,IAAI;IACtB;IAEA,gBAAgB;IAChB,MAAM,mBAAmB;QACvB,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC;QACvC,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,eAAe,EAAU,EAAE;QAC/B,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,GAAG,QAAQ,CAAC;QACvE,OAAO,SAAS,IAAI;IACtB;IAEA,MAAM,cAAc,EAAU,EAAE;QAC9B,MAAM,WAAW,MAAM,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC,gBAAgB,EAAE,GAAG,OAAO,CAAC;QACtE,OAAO,SAAS,IAAI;IACtB;IAEA,yBAAyB;IACzB,MAAM,QAAiB,MAA0B,EAA6B;QAC5E,OAAO,IAAI,CAAC,MAAM,CAAC,OAAO,CAAC;IAC7B;AACF;AAEO,MAAM,YAAY,IAAI;uCACd", "debugId": null}}, {"offset": {"line": 270, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/darna/frontend/src/contexts/AuthContext.tsx"], "sourcesContent": ["'use client'\n\nimport React, { createContext, useContext, useEffect, useState, ReactNode } from 'react'\nimport { User, AuthResponse } from '@/types'\nimport { apiClient } from '@/lib/api'\n\ninterface AuthContextType {\n  user: User | null\n  loading: boolean\n  login: (email: string, password: string) => Promise<AuthResponse>\n  register: (userData: {\n    name: string\n    email: string\n    password: string\n    password_confirmation: string\n    role?: string\n  }) => Promise<AuthResponse>\n  logout: () => Promise<void>\n  isAuthenticated: boolean\n  hasRole: (role: string) => boolean\n}\n\nconst AuthContext = createContext<AuthContextType | undefined>(undefined)\n\nexport function useAuth() {\n  const context = useContext(AuthContext)\n  if (context === undefined) {\n    throw new Error('useAuth must be used within an AuthProvider')\n  }\n  return context\n}\n\ninterface AuthProviderProps {\n  children: ReactNode\n}\n\nexport function AuthProvider({ children }: AuthProviderProps) {\n  const [user, setUser] = useState<User | null>(null)\n  const [loading, setLoading] = useState(true)\n\n  useEffect(() => {\n    checkAuthStatus()\n  }, [])\n\n  const checkAuthStatus = async () => {\n    try {\n      const token = localStorage.getItem('auth_token')\n      if (!token) {\n        setLoading(false)\n        return\n      }\n\n      // Verify token with backend\n      const response = await apiClient.request({\n        url: '/user',\n        method: 'GET',\n      })\n\n      if (response.data) {\n        setUser(response.data)\n      }\n    } catch (error) {\n      console.error('Auth check failed:', error)\n      localStorage.removeItem('auth_token')\n    } finally {\n      setLoading(false)\n    }\n  }\n\n  const login = async (email: string, password: string): Promise<AuthResponse> => {\n    try {\n      const response = await apiClient.login(email, password)\n      setUser(response.user)\n      return response\n    } catch (error) {\n      throw error\n    }\n  }\n\n  const register = async (userData: {\n    name: string\n    email: string\n    password: string\n    password_confirmation: string\n    role?: string\n  }): Promise<AuthResponse> => {\n    try {\n      const response = await apiClient.register(userData)\n      setUser(response.user)\n      return response\n    } catch (error) {\n      throw error\n    }\n  }\n\n  const logout = async (): Promise<void> => {\n    try {\n      await apiClient.logout()\n    } catch (error) {\n      console.error('Logout error:', error)\n    } finally {\n      setUser(null)\n    }\n  }\n\n  const isAuthenticated = !!user\n  const hasRole = (role: string) => user?.role === role\n\n  const value: AuthContextType = {\n    user,\n    loading,\n    login,\n    register,\n    logout,\n    isAuthenticated,\n    hasRole,\n  }\n\n  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>\n}\n\n// Higher-order component for protected routes\nexport function withAuth<P extends object>(\n  Component: React.ComponentType<P>,\n  allowedRoles?: string[]\n) {\n  return function AuthenticatedComponent(props: P) {\n    const { user, loading } = useAuth()\n\n    if (loading) {\n      return (\n        <div className=\"flex items-center justify-center min-h-screen\">\n          <div className=\"animate-spin rounded-full h-32 w-32 border-b-2 border-primary\"></div>\n        </div>\n      )\n    }\n\n    if (!user) {\n      // Redirect to login\n      if (typeof window !== 'undefined') {\n        window.location.href = '/auth/login'\n      }\n      return null\n    }\n\n    if (allowedRoles && !allowedRoles.includes(user.role)) {\n      return (\n        <div className=\"flex items-center justify-center min-h-screen\">\n          <div className=\"text-center\">\n            <h1 className=\"text-2xl font-bold text-destructive\">Access Denied</h1>\n            <p className=\"text-muted-foreground\">You don't have permission to access this page.</p>\n          </div>\n        </div>\n      )\n    }\n\n    return <Component {...props} />\n  }\n}\n"], "names": [], "mappings": ";;;;;;AAEA;AAEA;AAJA;;;;AAsBA,MAAM,4BAAc,CAAA,GAAA,qMAAA,CAAA,gBAAa,AAAD,EAA+B;AAExD,SAAS;IACd,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,aAAU,AAAD,EAAE;IAC3B,IAAI,YAAY,WAAW;QACzB,MAAM,IAAI,MAAM;IAClB;IACA,OAAO;AACT;AAMO,SAAS,aAAa,EAAE,QAAQ,EAAqB;IAC1D,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe;IAC9C,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG,EAAE;IAEL,MAAM,kBAAkB;QACtB,IAAI;YACF,MAAM,QAAQ,aAAa,OAAO,CAAC;YACnC,IAAI,CAAC,OAAO;gBACV,WAAW;gBACX;YACF;YAEA,4BAA4B;YAC5B,MAAM,WAAW,MAAM,iHAAA,CAAA,YAAS,CAAC,OAAO,CAAC;gBACvC,KAAK;gBACL,QAAQ;YACV;YAEA,IAAI,SAAS,IAAI,EAAE;gBACjB,QAAQ,SAAS,IAAI;YACvB;QACF,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,sBAAsB;YACpC,aAAa,UAAU,CAAC;QAC1B,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,QAAQ,OAAO,OAAe;QAClC,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,YAAS,CAAC,KAAK,CAAC,OAAO;YAC9C,QAAQ,SAAS,IAAI;YACrB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,WAAW,OAAO;QAOtB,IAAI;YACF,MAAM,WAAW,MAAM,iHAAA,CAAA,YAAS,CAAC,QAAQ,CAAC;YAC1C,QAAQ,SAAS,IAAI;YACrB,OAAO;QACT,EAAE,OAAO,OAAO;YACd,MAAM;QACR;IACF;IAEA,MAAM,SAAS;QACb,IAAI;YACF,MAAM,iHAAA,CAAA,YAAS,CAAC,MAAM;QACxB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,iBAAiB;QACjC,SAAU;YACR,QAAQ;QACV;IACF;IAEA,MAAM,kBAAkB,CAAC,CAAC;IAC1B,MAAM,UAAU,CAAC,OAAiB,MAAM,SAAS;IAEjD,MAAM,QAAyB;QAC7B;QACA;QACA;QACA;QACA;QACA;QACA;IACF;IAEA,qBAAO,8OAAC,YAAY,QAAQ;QAAC,OAAO;kBAAQ;;;;;;AAC9C;AAGO,SAAS,SACd,SAAiC,EACjC,YAAuB;IAEvB,OAAO,SAAS,uBAAuB,KAAQ;QAC7C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,GAAG;QAE1B,IAAI,SAAS;YACX,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;;;;;;;;;;QAGrB;QAEA,IAAI,CAAC,MAAM;YACT,oBAAoB;YACpB,uCAAmC;;YAEnC;YACA,OAAO;QACT;QAEA,IAAI,gBAAgB,CAAC,aAAa,QAAQ,CAAC,KAAK,IAAI,GAAG;YACrD,qBACE,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAG,WAAU;sCAAsC;;;;;;sCACpD,8OAAC;4BAAE,WAAU;sCAAwB;;;;;;;;;;;;;;;;;QAI7C;QAEA,qBAAO,8OAAC;YAAW,GAAG,KAAK;;;;;;IAC7B;AACF", "debugId": null}}, {"offset": {"line": 464, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/darna/frontend/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n\nexport function formatPrice(price: number): string {\n  return new Intl.NumberFormat('en-US', {\n    style: 'currency',\n    currency: 'USD',\n    minimumFractionDigits: 0,\n    maximumFractionDigits: 0,\n  }).format(price)\n}\n\nexport function formatDate(date: string | Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'long',\n    day: 'numeric',\n  }).format(new Date(date))\n}\n\nexport function formatDateTime(date: string | Date): string {\n  return new Intl.DateTimeFormat('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n    hour: '2-digit',\n    minute: '2-digit',\n  }).format(new Date(date))\n}\n\nexport function slugify(text: string): string {\n  return text\n    .toLowerCase()\n    .replace(/[^\\w ]+/g, '')\n    .replace(/ +/g, '-')\n}\n\nexport function truncateText(text: string, maxLength: number): string {\n  if (text.length <= maxLength) return text\n  return text.slice(0, maxLength) + '...'\n}\n\nexport function debounce<T extends (...args: any[]) => any>(\n  func: T,\n  wait: number\n): (...args: Parameters<T>) => void {\n  let timeout: NodeJS.Timeout\n  return (...args: Parameters<T>) => {\n    clearTimeout(timeout)\n    timeout = setTimeout(() => func(...args), wait)\n  }\n}\n\nexport function generateId(): string {\n  return Math.random().toString(36).substr(2, 9)\n}\n\nexport function isValidEmail(email: string): boolean {\n  const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/\n  return emailRegex.test(email)\n}\n\nexport function isValidPhone(phone: string): boolean {\n  const phoneRegex = /^[\\+]?[1-9][\\d]{0,15}$/\n  return phoneRegex.test(phone.replace(/\\s/g, ''))\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB;AAEO,SAAS,YAAY,KAAa;IACvC,OAAO,IAAI,KAAK,YAAY,CAAC,SAAS;QACpC,OAAO;QACP,UAAU;QACV,uBAAuB;QACvB,uBAAuB;IACzB,GAAG,MAAM,CAAC;AACZ;AAEO,SAAS,WAAW,IAAmB;IAC5C,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,eAAe,IAAmB;IAChD,OAAO,IAAI,KAAK,cAAc,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;QACL,MAAM;QACN,QAAQ;IACV,GAAG,MAAM,CAAC,IAAI,KAAK;AACrB;AAEO,SAAS,QAAQ,IAAY;IAClC,OAAO,KACJ,WAAW,GACX,OAAO,CAAC,YAAY,IACpB,OAAO,CAAC,OAAO;AACpB;AAEO,SAAS,aAAa,IAAY,EAAE,SAAiB;IAC1D,IAAI,KAAK,MAAM,IAAI,WAAW,OAAO;IACrC,OAAO,KAAK,KAAK,CAAC,GAAG,aAAa;AACpC;AAEO,SAAS,SACd,IAAO,EACP,IAAY;IAEZ,IAAI;IACJ,OAAO,CAAC,GAAG;QACT,aAAa;QACb,UAAU,WAAW,IAAM,QAAQ,OAAO;IAC5C;AACF;AAEO,SAAS;IACd,OAAO,KAAK,MAAM,GAAG,QAAQ,CAAC,IAAI,MAAM,CAAC,GAAG;AAC9C;AAEO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC;AACzB;AAEO,SAAS,aAAa,KAAa;IACxC,MAAM,aAAa;IACnB,OAAO,WAAW,IAAI,CAAC,MAAM,OAAO,CAAC,OAAO;AAC9C", "debugId": null}}, {"offset": {"line": 538, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/darna/frontend/src/components/ui/Button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50\",\n  {\n    variants: {\n      variant: {\n        default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n        outline:\n          \"border border-input bg-background hover:bg-accent hover:text-accent-foreground\",\n        secondary:\n          \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n        ghost: \"hover:bg-accent hover:text-accent-foreground\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n        gradient: \"bg-gradient-to-r from-primary to-accent text-primary-foreground hover:from-primary/90 hover:to-accent/90\",\n      },\n      size: {\n        default: \"h-10 px-4 py-2\",\n        sm: \"h-9 rounded-md px-3\",\n        lg: \"h-11 rounded-md px-8\",\n        xl: \"h-12 rounded-lg px-10 text-base\",\n        icon: \"h-10 w-10\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nexport interface ButtonProps\n  extends React.ButtonHTMLAttributes<HTMLButtonElement>,\n    VariantProps<typeof buttonVariants> {\n  asChild?: boolean\n  loading?: boolean\n}\n\nconst Button = React.forwardRef<HTMLButtonElement, ButtonProps>(\n  ({ className, variant, size, asChild = false, loading = false, children, disabled, ...props }, ref) => {\n    return (\n      <button\n        className={cn(buttonVariants({ variant, size, className }))}\n        ref={ref}\n        disabled={disabled || loading}\n        {...props}\n      >\n        {loading && (\n          <svg\n            className=\"animate-spin -ml-1 mr-2 h-4 w-4\"\n            xmlns=\"http://www.w3.org/2000/svg\"\n            fill=\"none\"\n            viewBox=\"0 0 24 24\"\n          >\n            <circle\n              className=\"opacity-25\"\n              cx=\"12\"\n              cy=\"12\"\n              r=\"10\"\n              stroke=\"currentColor\"\n              strokeWidth=\"4\"\n            ></circle>\n            <path\n              className=\"opacity-75\"\n              fill=\"currentColor\"\n              d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n            ></path>\n          </svg>\n        )}\n        {children}\n      </button>\n    )\n  }\n)\nButton.displayName = \"Button\"\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,0RACA;IACE,UAAU;QACR,SAAS;YACP,SAAS;YACT,aACE;YACF,SACE;YACF,WACE;YACF,OAAO;YACP,MAAM;YACN,UAAU;QACZ;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAUF,MAAM,uBAAS,CAAA,GAAA,qMAAA,CAAA,aAAgB,AAAD,EAC5B,CAAC,EAAE,SAAS,EAAE,OAAO,EAAE,IAAI,EAAE,UAAU,KAAK,EAAE,UAAU,KAAK,EAAE,QAAQ,EAAE,QAAQ,EAAE,GAAG,OAAO,EAAE;IAC7F,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACxD,KAAK;QACL,UAAU,YAAY;QACrB,GAAG,KAAK;;YAER,yBACC,8OAAC;gBACC,WAAU;gBACV,OAAM;gBACN,MAAK;gBACL,SAAQ;;kCAER,8OAAC;wBACC,WAAU;wBACV,IAAG;wBACH,IAAG;wBACH,GAAE;wBACF,QAAO;wBACP,aAAY;;;;;;kCAEd,8OAAC;wBACC,WAAU;wBACV,MAAK;wBACL,GAAE;;;;;;;;;;;;YAIP;;;;;;;AAGP;AAEF,OAAO,WAAW,GAAG", "debugId": null}}, {"offset": {"line": 634, "column": 0}, "map": {"version": 3, "sources": ["file:///Users/<USER>/Desktop/darna/frontend/src/components/layout/Header.tsx"], "sourcesContent": ["'use client'\n\nimport React, { useState } from 'react'\nimport Link from 'next/link'\nimport { useAuth } from '@/contexts/AuthContext'\nimport { Button } from '@/components/ui/Button'\nimport { \n  Home, \n  Search, \n  Heart, \n  User, \n  Menu, \n  X, \n  Building2, \n  Settings,\n  LogOut,\n  Plus\n} from 'lucide-react'\nimport { motion, AnimatePresence } from 'framer-motion'\n\nexport function Header() {\n  const { user, logout, isAuthenticated } = useAuth()\n  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)\n  const [isUserMenuOpen, setIsUserMenuOpen] = useState(false)\n\n  const handleLogout = async () => {\n    await logout()\n    setIsUserMenuOpen(false)\n  }\n\n  const navItems = [\n    { href: '/', label: 'Home', icon: Home },\n    { href: '/properties', label: 'Properties', icon: Search },\n    ...(isAuthenticated ? [\n      { href: '/favorites', label: 'Favorites', icon: Heart },\n    ] : []),\n  ]\n\n  const userMenuItems = [\n    ...(user?.role === 'developer' ? [\n      { href: '/developer/dashboard', label: 'Dashboard', icon: Building2 },\n      { href: '/developer/projects/new', label: 'Add Project', icon: Plus },\n    ] : []),\n    ...(user?.role === 'admin' ? [\n      { href: '/admin/dashboard', label: 'Admin Panel', icon: Settings },\n    ] : []),\n    ...(user?.role === 'user' ? [\n      { href: '/dashboard', label: 'Dashboard', icon: User },\n    ] : []),\n    { href: '/profile', label: 'Profile', icon: User },\n  ]\n\n  return (\n    <header className=\"sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60\">\n      <div className=\"container mx-auto px-4\">\n        <div className=\"flex h-16 items-center justify-between\">\n          {/* Logo */}\n          <Link href=\"/\" className=\"flex items-center space-x-2\">\n            <Building2 className=\"h-8 w-8 text-primary\" />\n            <span className=\"text-2xl font-bold gradient-text\">Darna</span>\n          </Link>\n\n          {/* Desktop Navigation */}\n          <nav className=\"hidden md:flex items-center space-x-6\">\n            {navItems.map((item) => (\n              <Link\n                key={item.href}\n                href={item.href}\n                className=\"flex items-center space-x-1 text-sm font-medium text-muted-foreground hover:text-foreground transition-colors\"\n              >\n                <item.icon className=\"h-4 w-4\" />\n                <span>{item.label}</span>\n              </Link>\n            ))}\n          </nav>\n\n          {/* Desktop Auth Section */}\n          <div className=\"hidden md:flex items-center space-x-4\">\n            {isAuthenticated ? (\n              <div className=\"relative\">\n                <Button\n                  variant=\"ghost\"\n                  size=\"sm\"\n                  onClick={() => setIsUserMenuOpen(!isUserMenuOpen)}\n                  className=\"flex items-center space-x-2\"\n                >\n                  <User className=\"h-4 w-4\" />\n                  <span>{user?.name}</span>\n                </Button>\n\n                <AnimatePresence>\n                  {isUserMenuOpen && (\n                    <motion.div\n                      initial={{ opacity: 0, y: -10 }}\n                      animate={{ opacity: 1, y: 0 }}\n                      exit={{ opacity: 0, y: -10 }}\n                      className=\"absolute right-0 mt-2 w-48 bg-background border rounded-md shadow-lg py-1 z-50\"\n                    >\n                      {userMenuItems.map((item) => (\n                        <Link\n                          key={item.href}\n                          href={item.href}\n                          className=\"flex items-center space-x-2 px-4 py-2 text-sm hover:bg-accent\"\n                          onClick={() => setIsUserMenuOpen(false)}\n                        >\n                          <item.icon className=\"h-4 w-4\" />\n                          <span>{item.label}</span>\n                        </Link>\n                      ))}\n                      <hr className=\"my-1\" />\n                      <button\n                        onClick={handleLogout}\n                        className=\"flex items-center space-x-2 w-full px-4 py-2 text-sm hover:bg-accent text-destructive\"\n                      >\n                        <LogOut className=\"h-4 w-4\" />\n                        <span>Logout</span>\n                      </button>\n                    </motion.div>\n                  )}\n                </AnimatePresence>\n              </div>\n            ) : (\n              <div className=\"flex items-center space-x-2\">\n                <Button variant=\"ghost\" size=\"sm\" asChild>\n                  <Link href=\"/auth/login\">Login</Link>\n                </Button>\n                <Button size=\"sm\" asChild>\n                  <Link href=\"/auth/register\">Sign Up</Link>\n                </Button>\n              </div>\n            )}\n          </div>\n\n          {/* Mobile Menu Button */}\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"md:hidden\"\n            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}\n          >\n            {isMobileMenuOpen ? <X className=\"h-6 w-6\" /> : <Menu className=\"h-6 w-6\" />}\n          </Button>\n        </div>\n\n        {/* Mobile Navigation */}\n        <AnimatePresence>\n          {isMobileMenuOpen && (\n            <motion.div\n              initial={{ opacity: 0, height: 0 }}\n              animate={{ opacity: 1, height: 'auto' }}\n              exit={{ opacity: 0, height: 0 }}\n              className=\"md:hidden border-t py-4\"\n            >\n              <nav className=\"flex flex-col space-y-4\">\n                {navItems.map((item) => (\n                  <Link\n                    key={item.href}\n                    href={item.href}\n                    className=\"flex items-center space-x-2 text-sm font-medium text-muted-foreground hover:text-foreground\"\n                    onClick={() => setIsMobileMenuOpen(false)}\n                  >\n                    <item.icon className=\"h-4 w-4\" />\n                    <span>{item.label}</span>\n                  </Link>\n                ))}\n\n                {isAuthenticated ? (\n                  <>\n                    <hr className=\"my-2\" />\n                    {userMenuItems.map((item) => (\n                      <Link\n                        key={item.href}\n                        href={item.href}\n                        className=\"flex items-center space-x-2 text-sm font-medium text-muted-foreground hover:text-foreground\"\n                        onClick={() => setIsMobileMenuOpen(false)}\n                      >\n                        <item.icon className=\"h-4 w-4\" />\n                        <span>{item.label}</span>\n                      </Link>\n                    ))}\n                    <button\n                      onClick={() => {\n                        handleLogout()\n                        setIsMobileMenuOpen(false)\n                      }}\n                      className=\"flex items-center space-x-2 text-sm font-medium text-destructive\"\n                    >\n                      <LogOut className=\"h-4 w-4\" />\n                      <span>Logout</span>\n                    </button>\n                  </>\n                ) : (\n                  <>\n                    <hr className=\"my-2\" />\n                    <Link\n                      href=\"/auth/login\"\n                      className=\"text-sm font-medium text-muted-foreground hover:text-foreground\"\n                      onClick={() => setIsMobileMenuOpen(false)}\n                    >\n                      Login\n                    </Link>\n                    <Link\n                      href=\"/auth/register\"\n                      className=\"text-sm font-medium text-primary\"\n                      onClick={() => setIsMobileMenuOpen(false)}\n                    >\n                      Sign Up\n                    </Link>\n                  </>\n                )}\n              </nav>\n            </motion.div>\n          )}\n        </AnimatePresence>\n      </div>\n    </header>\n  )\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAYA;AAAA;AAlBA;;;;;;;;AAoBO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAChD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAErD,MAAM,eAAe;QACnB,MAAM;QACN,kBAAkB;IACpB;IAEA,MAAM,WAAW;QACf;YAAE,MAAM;YAAK,OAAO;YAAQ,MAAM,mMAAA,CAAA,OAAI;QAAC;QACvC;YAAE,MAAM;YAAe,OAAO;YAAc,MAAM,sMAAA,CAAA,SAAM;QAAC;WACrD,kBAAkB;YACpB;gBAAE,MAAM;gBAAc,OAAO;gBAAa,MAAM,oMAAA,CAAA,QAAK;YAAC;SACvD,GAAG,EAAE;KACP;IAED,MAAM,gBAAgB;WAChB,MAAM,SAAS,cAAc;YAC/B;gBAAE,MAAM;gBAAwB,OAAO;gBAAa,MAAM,gNAAA,CAAA,YAAS;YAAC;YACpE;gBAAE,MAAM;gBAA2B,OAAO;gBAAe,MAAM,kMAAA,CAAA,OAAI;YAAC;SACrE,GAAG,EAAE;WACF,MAAM,SAAS,UAAU;YAC3B;gBAAE,MAAM;gBAAoB,OAAO;gBAAe,MAAM,0MAAA,CAAA,WAAQ;YAAC;SAClE,GAAG,EAAE;WACF,MAAM,SAAS,SAAS;YAC1B;gBAAE,MAAM;gBAAc,OAAO;gBAAa,MAAM,kMAAA,CAAA,OAAI;YAAC;SACtD,GAAG,EAAE;QACN;YAAE,MAAM;YAAY,OAAO;YAAW,MAAM,kMAAA,CAAA,OAAI;QAAC;KAClD;IAED,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;;8CACvB,8OAAC,gNAAA,CAAA,YAAS;oCAAC,WAAU;;;;;;8CACrB,8OAAC;oCAAK,WAAU;8CAAmC;;;;;;;;;;;;sCAIrD,8OAAC;4BAAI,WAAU;sCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;oCAEH,MAAM,KAAK,IAAI;oCACf,WAAU;;sDAEV,8OAAC,KAAK,IAAI;4CAAC,WAAU;;;;;;sDACrB,8OAAC;sDAAM,KAAK,KAAK;;;;;;;mCALZ,KAAK,IAAI;;;;;;;;;;sCAWpB,8OAAC;4BAAI,WAAU;sCACZ,gCACC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCACL,SAAQ;wCACR,MAAK;wCACL,SAAS,IAAM,kBAAkB,CAAC;wCAClC,WAAU;;0DAEV,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAM,MAAM;;;;;;;;;;;;kDAGf,8OAAC,yLAAA,CAAA,kBAAe;kDACb,gCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;4CACT,SAAS;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC9B,SAAS;gDAAE,SAAS;gDAAG,GAAG;4CAAE;4CAC5B,MAAM;gDAAE,SAAS;gDAAG,GAAG,CAAC;4CAAG;4CAC3B,WAAU;;gDAET,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC,4JAAA,CAAA,UAAI;wDAEH,MAAM,KAAK,IAAI;wDACf,WAAU;wDACV,SAAS,IAAM,kBAAkB;;0EAEjC,8OAAC,KAAK,IAAI;gEAAC,WAAU;;;;;;0EACrB,8OAAC;0EAAM,KAAK,KAAK;;;;;;;uDANZ,KAAK,IAAI;;;;;8DASlB,8OAAC;oDAAG,WAAU;;;;;;8DACd,8OAAC;oDACC,SAAS;oDACT,WAAU;;sEAEV,8OAAC,0MAAA,CAAA,SAAM;4DAAC,WAAU;;;;;;sEAClB,8OAAC;sEAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;qDAOhB,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAQ,MAAK;wCAAK,OAAO;kDACvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAc;;;;;;;;;;;kDAE3B,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,OAAO;kDACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;sDAAiB;;;;;;;;;;;;;;;;;;;;;;sCAOpC,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;4BACV,SAAS,IAAM,oBAAoB,CAAC;sCAEnC,iCAAmB,8OAAC,4LAAA,CAAA,IAAC;gCAAC,WAAU;;;;;qDAAe,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;;8BAKpE,8OAAC,yLAAA,CAAA,kBAAe;8BACb,kCACC,8OAAC,0LAAA,CAAA,SAAM,CAAC,GAAG;wBACT,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBACjC,SAAS;4BAAE,SAAS;4BAAG,QAAQ;wBAAO;wBACtC,MAAM;4BAAE,SAAS;4BAAG,QAAQ;wBAAE;wBAC9B,WAAU;kCAEV,cAAA,8OAAC;4BAAI,WAAU;;gCACZ,SAAS,GAAG,CAAC,CAAC,qBACb,8OAAC,4JAAA,CAAA,UAAI;wCAEH,MAAM,KAAK,IAAI;wCACf,WAAU;wCACV,SAAS,IAAM,oBAAoB;;0DAEnC,8OAAC,KAAK,IAAI;gDAAC,WAAU;;;;;;0DACrB,8OAAC;0DAAM,KAAK,KAAK;;;;;;;uCANZ,KAAK,IAAI;;;;;gCAUjB,gCACC;;sDACE,8OAAC;4CAAG,WAAU;;;;;;wCACb,cAAc,GAAG,CAAC,CAAC,qBAClB,8OAAC,4JAAA,CAAA,UAAI;gDAEH,MAAM,KAAK,IAAI;gDACf,WAAU;gDACV,SAAS,IAAM,oBAAoB;;kEAEnC,8OAAC,KAAK,IAAI;wDAAC,WAAU;;;;;;kEACrB,8OAAC;kEAAM,KAAK,KAAK;;;;;;;+CANZ,KAAK,IAAI;;;;;sDASlB,8OAAC;4CACC,SAAS;gDACP;gDACA,oBAAoB;4CACtB;4CACA,WAAU;;8DAEV,8OAAC,0MAAA,CAAA,SAAM;oDAAC,WAAU;;;;;;8DAClB,8OAAC;8DAAK;;;;;;;;;;;;;iEAIV;;sDACE,8OAAC;4CAAG,WAAU;;;;;;sDACd,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,oBAAoB;sDACpC;;;;;;sDAGD,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAK;4CACL,WAAU;4CACV,SAAS,IAAM,oBAAoB;sDACpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAYrB", "debugId": null}}]}
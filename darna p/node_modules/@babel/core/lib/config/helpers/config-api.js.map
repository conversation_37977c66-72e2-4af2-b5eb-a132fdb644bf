{"version": 3, "names": ["_semver", "data", "require", "_index", "_caching", "makeConfigAPI", "cache", "env", "value", "using", "undefined", "envName", "assertSimpleType", "Array", "isArray", "some", "entry", "Error", "caller", "cb", "version", "coreVersion", "simple", "async", "assertVersion", "makePresetAPI", "externalDependencies", "targets", "JSON", "parse", "stringify", "addExternalDependency", "ref", "push", "Object", "assign", "makePluginAPI", "assumption", "name", "assumptions", "range", "Number", "isInteger", "semver", "satisfies", "limit", "stackTraceLimit", "err", "code"], "sources": ["../../../src/config/helpers/config-api.ts"], "sourcesContent": ["import semver from \"semver\";\nimport type { Targets } from \"@babel/helper-compilation-targets\";\n\nimport { version as coreVersion } from \"../../index.ts\";\nimport { assertSimpleType } from \"../caching.ts\";\nimport type {\n  CacheConfigurator,\n  SimpleCacheConfigurator,\n  SimpleType,\n} from \"../caching.ts\";\n\nimport type { AssumptionName, CallerMetadata } from \"../validation/options.ts\";\n\nimport type * as Context from \"../cache-contexts\";\n\ntype EnvFunction = {\n  (): string;\n  <T>(extractor: (babelEnv: string) => T): T;\n  (envVar: string): boolean;\n  (envVars: Array<string>): boolean;\n};\n\ntype CallerFactory = {\n  <T extends SimpleType>(\n    extractor: (callerMetadata: CallerMetadata | undefined) => T,\n  ): T;\n  (\n    extractor: (callerMetadata: CallerMetadata | undefined) => unknown,\n  ): SimpleType;\n};\ntype TargetsFunction = () => Targets;\ntype AssumptionFunction = (name: AssumptionName) => boolean | undefined;\n\nexport type ConfigAPI = {\n  version: string;\n  cache: SimpleCacheConfigurator;\n  env: EnvFunction;\n  async: () => boolean;\n  assertVersion: typeof assertVersion;\n  caller?: CallerFactory;\n};\n\nexport type PresetAPI = {\n  targets: TargetsFunction;\n  addExternalDependency: (ref: string) => void;\n} & ConfigAPI;\n\nexport type PluginAPI = {\n  assumption: AssumptionFunction;\n} & PresetAPI;\n\nexport function makeConfigAPI<SideChannel extends Context.SimpleConfig>(\n  cache: CacheConfigurator<SideChannel>,\n): ConfigAPI {\n  // TODO(@nicolo-ribaudo): If we remove the explicit type from `value`\n  // and the `as any` type cast, TypeScript crashes in an infinite\n  // recursion. After upgrading to TS4.7 and finishing the noImplicitAny\n  // PR, we should check if it still crashes and report it to the TS team.\n  const env: EnvFunction = ((\n    value: string | string[] | (<T>(babelEnv: string) => T),\n  ) =>\n    cache.using(data => {\n      if (value === undefined) return data.envName;\n      if (typeof value === \"function\") {\n        return assertSimpleType(value(data.envName));\n      }\n      return (Array.isArray(value) ? value : [value]).some(entry => {\n        if (typeof entry !== \"string\") {\n          throw new Error(\"Unexpected non-string value\");\n        }\n        return entry === data.envName;\n      });\n    })) as any;\n\n  const caller = (\n    cb: (CallerMetadata: CallerMetadata | undefined) => SimpleType,\n  ) => cache.using(data => assertSimpleType(cb(data.caller)));\n\n  return {\n    version: coreVersion,\n    cache: cache.simple(),\n    // Expose \".env()\" so people can easily get the same env that we expose using the \"env\" key.\n    env,\n    async: () => false,\n    caller,\n    assertVersion,\n  };\n}\n\nexport function makePresetAPI<SideChannel extends Context.SimplePreset>(\n  cache: CacheConfigurator<SideChannel>,\n  externalDependencies: Array<string>,\n): PresetAPI {\n  const targets = () =>\n    // We are using JSON.parse/JSON.stringify because it's only possible to cache\n    // primitive values. We can safely stringify the targets object because it\n    // only contains strings as its properties.\n    // Please make the Record and Tuple proposal happen!\n    JSON.parse(cache.using(data => JSON.stringify(data.targets)));\n\n  const addExternalDependency = (ref: string) => {\n    externalDependencies.push(ref);\n  };\n\n  return { ...makeConfigAPI(cache), targets, addExternalDependency };\n}\n\nexport function makePluginAPI<SideChannel extends Context.SimplePlugin>(\n  cache: CacheConfigurator<SideChannel>,\n  externalDependencies: Array<string>,\n): PluginAPI {\n  const assumption = (name: string) =>\n    cache.using(data => data.assumptions[name]);\n\n  return { ...makePresetAPI(cache, externalDependencies), assumption };\n}\n\nfunction assertVersion(range: string | number): void {\n  if (typeof range === \"number\") {\n    if (!Number.isInteger(range)) {\n      throw new Error(\"Expected string or integer value.\");\n    }\n    range = `^${range}.0.0-0`;\n  }\n  if (typeof range !== \"string\") {\n    throw new Error(\"Expected string or integer value.\");\n  }\n\n  // We want \"*\" to also allow any pre-release, but we do not pass\n  // the includePrerelease option to semver.satisfies because we\n  // do not want ^7.0.0 to match 8.0.0-alpha.1.\n  if (range === \"*\" || semver.satisfies(coreVersion, range)) return;\n\n  const limit = Error.stackTraceLimit;\n\n  if (typeof limit === \"number\" && limit < 25) {\n    // Bump up the limit if needed so that users are more likely\n    // to be able to see what is calling Babel.\n    Error.stackTraceLimit = 25;\n  }\n\n  const err = new Error(\n    `Requires Babel \"${range}\", but was loaded with \"${coreVersion}\". ` +\n      `If you are sure you have a compatible version of @babel/core, ` +\n      `it is likely that something in your build process is loading the ` +\n      `wrong version. Inspect the stack trace of this error to look for ` +\n      `the first entry that doesn't mention \"@babel/core\" or \"babel-core\" ` +\n      `to see what is calling Babel.`,\n  );\n\n  if (typeof limit === \"number\") {\n    Error.stackTraceLimit = limit;\n  }\n\n  throw Object.assign(err, {\n    code: \"BABEL_VERSION_UNSUPPORTED\",\n    version: coreVersion,\n    range,\n  });\n}\n"], "mappings": ";;;;;;;;AAAA,SAAAA,QAAA;EAAA,MAAAC,IAAA,GAAAC,OAAA;EAAAF,OAAA,YAAAA,CAAA;IAAA,OAAAC,IAAA;EAAA;EAAA,OAAAA,IAAA;AAAA;AAGA,IAAAE,MAAA,GAAAD,OAAA;AACA,IAAAE,QAAA,GAAAF,OAAA;AA+CO,SAASG,aAAaA,CAC3BC,KAAqC,EAC1B;EAKX,MAAMC,GAAgB,GACpBC,KAAuD,IAEvDF,KAAK,CAACG,KAAK,CAACR,IAAI,IAAI;IAClB,IAAIO,KAAK,KAAKE,SAAS,EAAE,OAAOT,IAAI,CAACU,OAAO;IAC5C,IAAI,OAAOH,KAAK,KAAK,UAAU,EAAE;MAC/B,OAAO,IAAAI,yBAAgB,EAACJ,KAAK,CAACP,IAAI,CAACU,OAAO,CAAC,CAAC;IAC9C;IACA,OAAO,CAACE,KAAK,CAACC,OAAO,CAACN,KAAK,CAAC,GAAGA,KAAK,GAAG,CAACA,KAAK,CAAC,EAAEO,IAAI,CAACC,KAAK,IAAI;MAC5D,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;QAC7B,MAAM,IAAIC,KAAK,CAAC,6BAA6B,CAAC;MAChD;MACA,OAAOD,KAAK,KAAKf,IAAI,CAACU,OAAO;IAC/B,CAAC,CAAC;EACJ,CAAC,CAAS;EAEZ,MAAMO,MAAM,GACVC,EAA8D,IAC3Db,KAAK,CAACG,KAAK,CAACR,IAAI,IAAI,IAAAW,yBAAgB,EAACO,EAAE,CAAClB,IAAI,CAACiB,MAAM,CAAC,CAAC,CAAC;EAE3D,OAAO;IACLE,OAAO,EAAEC,cAAW;IACpBf,KAAK,EAAEA,KAAK,CAACgB,MAAM,CAAC,CAAC;IAErBf,GAAG;IACHgB,KAAK,EAAEA,CAAA,KAAM,KAAK;IAClBL,MAAM;IACNM;EACF,CAAC;AACH;AAEO,SAASC,aAAaA,CAC3BnB,KAAqC,EACrCoB,oBAAmC,EACxB;EACX,MAAMC,OAAO,GAAGA,CAAA,KAKdC,IAAI,CAACC,KAAK,CAACvB,KAAK,CAACG,KAAK,CAACR,IAAI,IAAI2B,IAAI,CAACE,SAAS,CAAC7B,IAAI,CAAC0B,OAAO,CAAC,CAAC,CAAC;EAE/D,MAAMI,qBAAqB,GAAIC,GAAW,IAAK;IAC7CN,oBAAoB,CAACO,IAAI,CAACD,GAAG,CAAC;EAChC,CAAC;EAED,OAAAE,MAAA,CAAAC,MAAA,KAAY9B,aAAa,CAACC,KAAK,CAAC;IAAEqB,OAAO;IAAEI;EAAqB;AAClE;AAEO,SAASK,aAAaA,CAC3B9B,KAAqC,EACrCoB,oBAAmC,EACxB;EACX,MAAMW,UAAU,GAAIC,IAAY,IAC9BhC,KAAK,CAACG,KAAK,CAACR,IAAI,IAAIA,IAAI,CAACsC,WAAW,CAACD,IAAI,CAAC,CAAC;EAE7C,OAAAJ,MAAA,CAAAC,MAAA,KAAYV,aAAa,CAACnB,KAAK,EAAEoB,oBAAoB,CAAC;IAAEW;EAAU;AACpE;AAEA,SAASb,aAAaA,CAACgB,KAAsB,EAAQ;EACnD,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,IAAI,CAACC,MAAM,CAACC,SAAS,CAACF,KAAK,CAAC,EAAE;MAC5B,MAAM,IAAIvB,KAAK,CAAC,mCAAmC,CAAC;IACtD;IACAuB,KAAK,GAAG,IAAIA,KAAK,QAAQ;EAC3B;EACA,IAAI,OAAOA,KAAK,KAAK,QAAQ,EAAE;IAC7B,MAAM,IAAIvB,KAAK,CAAC,mCAAmC,CAAC;EACtD;EAKA,IAAIuB,KAAK,KAAK,GAAG,IAAIG,QAAKA,CAAC,CAACC,SAAS,CAACvB,cAAW,EAAEmB,KAAK,CAAC,EAAE;EAE3D,MAAMK,KAAK,GAAG5B,KAAK,CAAC6B,eAAe;EAEnC,IAAI,OAAOD,KAAK,KAAK,QAAQ,IAAIA,KAAK,GAAG,EAAE,EAAE;IAG3C5B,KAAK,CAAC6B,eAAe,GAAG,EAAE;EAC5B;EAEA,MAAMC,GAAG,GAAG,IAAI9B,KAAK,CACnB,mBAAmBuB,KAAK,2BAA2BnB,cAAW,KAAK,GACjE,gEAAgE,GAChE,mEAAmE,GACnE,mEAAmE,GACnE,qEAAqE,GACrE,+BACJ,CAAC;EAED,IAAI,OAAOwB,KAAK,KAAK,QAAQ,EAAE;IAC7B5B,KAAK,CAAC6B,eAAe,GAAGD,KAAK;EAC/B;EAEA,MAAMX,MAAM,CAACC,MAAM,CAACY,GAAG,EAAE;IACvBC,IAAI,EAAE,2BAA2B;IACjC5B,OAAO,EAAEC,cAAW;IACpBmB;EACF,CAAC,CAAC;AACJ;AAAC", "ignoreList": []}
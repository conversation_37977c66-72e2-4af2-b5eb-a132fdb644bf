<?php

use Illuminate\Http\Request;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\AuthController;
use App\Http\Controllers\PropertyController;
use App\Http\Controllers\Admin\ProjectController as AdminProjectController;
use App\Http\Controllers\Developer\ProjectController as DeveloperProjectController;
use App\Http\Controllers\User\BookingController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

// Authentication
Route::post('/register', [AuthController::class, 'register']);
Route::post('/login', [AuthController::class, 'login']);
Route::post('/logout', [AuthController::class, 'logout'])->middleware('auth:sanctum');
Route::get('/user', [AuthController::class, 'user'])->middleware('auth:sanctum');

// Public routes
Route::get('/properties', [PropertyController::class, 'index']);
Route::get('/properties/{property}', [PropertyController::class, 'show']);

// Admin routes
Route::middleware(['auth:sanctum', 'role:admin'])->prefix('admin')->group(function () {
    Route::get('/projects', [AdminProjectController::class, 'index']);
    Route::post('/projects/{project}/approve', [AdminProjectController::class, 'approve']);
    Route::post('/projects/{project}/reject', [AdminProjectController::class, 'reject']);
});

// Developer routes
Route::middleware(['auth:sanctum', 'role:developer'])->prefix('developer')->group(function () {
    Route::apiResource('/projects', DeveloperProjectController::class);
    // Image upload route
    Route::post('/projects/{project}/upload-image', [DeveloperProjectController::class, 'uploadImage']);
});

// User routes
Route::middleware(['auth:sanctum', 'role:user'])->prefix('user')->group(function () {
    Route::post('/properties/{property}/like', [PropertyController::class, 'like']);
    Route::post('/properties/{property}/book', [BookingController::class, 'store']);
});
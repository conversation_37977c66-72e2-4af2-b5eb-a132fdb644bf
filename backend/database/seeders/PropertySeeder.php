<?php

namespace Database\Seeders;

use App\Models\Property;
use App\Models\Project;
use App\Models\User;
use App\Models\DeveloperProfile;
use Illuminate\Database\Seeder;

class PropertySeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        // Create a sample developer user
        $developer = User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Sample Developer',
                'password' => bcrypt('password'),
                'role' => 'developer',
            ]
        );

        // Create developer profile
        $developerProfile = DeveloperProfile::firstOrCreate(
            ['user_id' => $developer->id],
            [
                'company_name' => 'Green Valley Developments',
                'description' => 'Leading real estate developer specializing in luxury residential projects',
                'verified_at' => now(),
            ]
        );

        // Create a sample project
        $project = Project::create([
            'developer_profile_id' => $developerProfile->id,
            'name' => 'Green Valley Residences',
            'description' => 'A modern residential complex with luxury amenities',
            'location' => 'Downtown District',
            'status' => 'active',
        ]);

        // Create sample properties
        $properties = [
            [
                'project_id' => $project->id,
                'title' => 'Luxury 2-Bedroom Apartment',
                'description' => 'Spacious 2-bedroom apartment with modern amenities and city views.',
                'type' => 'apartment',
                'price' => 450000,
                'size_sqm' => 120.5,
                'bedrooms' => 2,
                'bathrooms' => 2,
            ],
            [
                'project_id' => $project->id,
                'title' => 'Executive 3-Bedroom Villa',
                'description' => 'Beautiful villa with private garden and premium finishes.',
                'type' => 'villa',
                'price' => 850000,
                'size_sqm' => 250.0,
                'bedrooms' => 3,
                'bathrooms' => 3,
            ],
            [
                'project_id' => $project->id,
                'title' => 'Modern 1-Bedroom Studio',
                'description' => 'Compact and efficient studio perfect for young professionals.',
                'type' => 'apartment',
                'price' => 280000,
                'size_sqm' => 65.0,
                'bedrooms' => 1,
                'bathrooms' => 1,
            ],
            [
                'project_id' => $project->id,
                'title' => 'Family 4-Bedroom Townhouse',
                'description' => 'Spacious townhouse ideal for families with children.',
                'type' => 'townhouse',
                'price' => 650000,
                'size_sqm' => 200.0,
                'bedrooms' => 4,
                'bathrooms' => 3,
            ],
            [
                'project_id' => $project->id,
                'title' => 'Penthouse with Panoramic Views',
                'description' => 'Exclusive penthouse with 360-degree city views and premium amenities.',
                'type' => 'penthouse',
                'price' => 1200000,
                'size_sqm' => 350.0,
                'bedrooms' => 4,
                'bathrooms' => 4,
            ],
        ];

        foreach ($properties as $propertyData) {
            Property::create($propertyData);
        }
    }
}

<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Property extends Model
{
    use HasFactory;

    protected $fillable = [
        'project_id',
        'title',
        'description',
        'type',
        'price',
        'size_sqm',
        'bedrooms',
        'bathrooms',
    ];

    protected $casts = [
        'price' => 'decimal:2',
        'size_sqm' => 'decimal:2',
    ];

    public function project()
    {
        return $this->belongsTo(Project::class);
    }

    public function media()
    {
        return $this->morphMany(Media::class, 'mediable');
    }

    public function bookings()
    {
        return $this->hasMany(Booking::class);
    }

    public function likes()
    {
        return $this->hasMany(Like::class);
    }
}

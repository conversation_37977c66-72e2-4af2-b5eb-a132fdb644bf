<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Project extends Model
{
    use HasFactory;

    protected $fillable = [
        'developer_profile_id',
        'name',
        'description',
        'location',
        'status',
        'completion_date',
        'total_units',
        'available_units',
    ];

    protected $casts = [
        'completion_date' => 'date',
    ];

    public function developerProfile()
    {
        return $this->belongsTo(DeveloperProfile::class);
    }

    public function properties()
    {
        return $this->hasMany(Property::class);
    }
}
